import { renderHTMLTemplate } from '../utils/template';

export function getAdminPage(): string {
  const content = `
    <div class="bg-gray-50 min-h-screen">
      <!-- 顶部导航栏 -->
      <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between items-center h-16">
            <div class="flex items-center">
              <h1 class="text-xl font-semibold text-gray-900">管理员面板</h1>
            </div>
            <div class="flex items-center space-x-4">
              <button id="adminInfoBtn" class="text-sm text-gray-500 hover:text-gray-700 cursor-pointer transition-colors">管理员</button>
              <button id="logoutBtn" class="text-sm text-red-600 hover:text-red-800">退出登录</button>
            </div>
          </div>
        </div>
      </nav>

      <!-- 主要内容 -->
      <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h2 class="text-2xl font-bold text-gray-900">用户管理</h2>
            <p class="mt-1 text-sm text-gray-600">查看和管理所有注册用户</p>
          </div>

          <!-- 统计卡片 -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">👥</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">总用户数</dt>
                      <dd class="text-lg font-medium text-gray-900" id="totalUsers">-</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">📅</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">今日新增</dt>
                      <dd class="text-lg font-medium text-gray-900" id="todayUsers">-</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                      <span class="text-white text-sm font-medium">📊</span>
                    </div>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">本月新增</dt>
                      <dd class="text-lg font-medium text-gray-900" id="monthUsers">-</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 用户列表 -->
          <div class="bg-white shadow overflow-hidden sm:rounded-md">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
              <div class="flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">注册用户列表</h3>
                <button id="refreshBtn" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                  <span class="mr-2">🔄</span>
                  刷新
                </button>
              </div>
            </div>
            
            <!-- 加载状态 -->
            <div id="loadingState" class="px-4 py-8 text-center">
              <div class="inline-flex items-center">
                <span class="loading-spinner mr-2"></span>
                <span class="text-gray-600">加载中...</span>
              </div>
            </div>

            <!-- 用户列表内容 -->
            <div id="usersListContainer" class="hidden">
              <ul id="usersList" class="divide-y divide-gray-200">
                <!-- 用户列表将通过JavaScript动态加载 -->
              </ul>
              
              <!-- 空状态 -->
              <div id="emptyState" class="hidden px-4 py-8 text-center">
                <div class="text-gray-400 text-6xl mb-4">👤</div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">暂无用户</h3>
                <p class="text-gray-600">还没有用户注册</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 修改管理员信息对话框 -->
    <div id="adminSettingsModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-screen overflow-y-auto">
        <div class="p-6">
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-semibold text-gray-900">修改管理员信息</h3>
            <button id="closeModalBtn" class="text-gray-400 hover:text-gray-600 transition-colors">
              <span class="text-2xl">&times;</span>
            </button>
          </div>
          
          <form id="adminSettingsForm" class="space-y-4">
            <div class="input-container">
              <label class="block text-sm font-medium text-gray-700 mb-2">当前用户名</label>
              <input 
                id="currentUsername"
                type="text" 
                value="admin"
                disabled
                class="w-full px-4 py-3 rounded-lg bg-gray-100 text-gray-500 cursor-not-allowed"
              >
            </div>
            
            <div class="input-container">
              <label class="block text-sm font-medium text-gray-700 mb-2">新用户名</label>
              <input 
                id="newUsername"
                type="text" 
                placeholder="输入新的用户名"
                class="w-full px-4 py-3 rounded-lg text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
            </div>
            
            <div class="input-container">
              <label class="block text-sm font-medium text-gray-700 mb-2">当前密码</label>
              <input 
                id="currentPassword"
                type="password" 
                placeholder="输入当前密码"
                class="w-full px-4 py-3 rounded-lg text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
            </div>
            
            <div class="input-container">
              <label class="block text-sm font-medium text-gray-700 mb-2">新密码</label>
              <input 
                id="newPassword"
                type="password" 
                placeholder="输入新密码（至少6位）"
                class="w-full px-4 py-3 rounded-lg text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
            </div>
            
            <div class="input-container">
              <label class="block text-sm font-medium text-gray-700 mb-2">确认新密码</label>
              <input 
                id="confirmPassword"
                type="password" 
                placeholder="再次输入新密码"
                class="w-full px-4 py-3 rounded-lg text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
            </div>
            
            <div class="flex space-x-3 pt-4">
              <button type="button" id="cancelBtn" class="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                取消
              </button>
              <button type="submit" id="saveBtn" class="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                保存修改
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 通用确认对话框 -->
    <div id="confirmModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-6">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
              <span class="text-yellow-600 text-xl">⚠</span>
            </div>
            <h3 id="confirmTitle" class="text-lg font-semibold text-gray-900">确认操作</h3>
          </div>
          
          <p id="confirmMessage" class="text-gray-600 mb-6">确定要执行此操作吗？</p>
          
          <div class="flex space-x-3">
            <button id="confirmCancel" class="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
              取消
            </button>
            <button id="confirmOk" class="flex-1 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              确认
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入对话框 -->
    <div id="promptModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="p-6">
          <div class="flex items-center mb-4">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
              <span class="text-blue-600 text-xl">ℹ</span>
            </div>
            <h3 id="promptTitle" class="text-lg font-semibold text-gray-900">输入信息</h3>
          </div>
          
          <p id="promptMessage" class="text-gray-600 mb-4">请输入信息：</p>
          
          <input 
            id="promptInput" 
            type="text" 
            class="w-full px-4 py-3 rounded-lg border border-gray-300 text-gray-600 focus:outline-none focus:border-blue-500 transition-all duration-200 mb-6"
            placeholder="请输入..."
          >
          
          <div class="flex space-x-3">
            <button id="promptCancel" class="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
              取消
            </button>
            <button id="promptOk" class="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              确认
            </button>
          </div>
        </div>
      </div>
    </div>

    <script>
      console.log('Admin page script loading...');
      
      // 自定义对话框函数
      function showConfirm(title, message, onConfirm, onCancel = null) {
        return new Promise((resolve) => {
          const modal = document.getElementById('confirmModal');
          const titleEl = document.getElementById('confirmTitle');
          const messageEl = document.getElementById('confirmMessage');
          const confirmBtn = document.getElementById('confirmOk');
          const cancelBtn = document.getElementById('confirmCancel');
          
          titleEl.textContent = title;
          messageEl.textContent = message;
          modal.classList.remove('hidden');
          
          const handleConfirm = () => {
            modal.classList.add('hidden');
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            if (onConfirm) onConfirm();
            resolve(true);
          };
          
          const handleCancel = () => {
            modal.classList.add('hidden');
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            if (onCancel) onCancel();
            resolve(false);
          };
          
          confirmBtn.addEventListener('click', handleConfirm);
          cancelBtn.addEventListener('click', handleCancel);
          
          // 点击背景关闭
          modal.addEventListener('click', (e) => {
            if (e.target === modal) {
              handleCancel();
            }
          });
        });
      }
      
      function showPrompt(title, message, defaultValue = '', onConfirm = null, onCancel = null) {
        return new Promise((resolve) => {
          const modal = document.getElementById('promptModal');
          const titleEl = document.getElementById('promptTitle');
          const messageEl = document.getElementById('promptMessage');
          const inputEl = document.getElementById('promptInput');
          const confirmBtn = document.getElementById('promptOk');
          const cancelBtn = document.getElementById('promptCancel');
          
          titleEl.textContent = title;
          messageEl.textContent = message;
          inputEl.value = defaultValue;
          modal.classList.remove('hidden');
          
          // 聚焦输入框
          setTimeout(() => inputEl.focus(), 100);
          
          const handleConfirm = () => {
            const value = inputEl.value.trim();
            modal.classList.add('hidden');
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            inputEl.removeEventListener('keypress', handleKeypress);
            if (onConfirm) onConfirm(value);
            resolve(value);
          };
          
          const handleCancel = () => {
            modal.classList.add('hidden');
            confirmBtn.removeEventListener('click', handleConfirm);
            cancelBtn.removeEventListener('click', handleCancel);
            inputEl.removeEventListener('keypress', handleKeypress);
            if (onCancel) onCancel();
            resolve(null);
          };
          
          const handleKeypress = (e) => {
            if (e.key === 'Enter') {
              handleConfirm();
            } else if (e.key === 'Escape') {
              handleCancel();
            }
          };
          
          confirmBtn.addEventListener('click', handleConfirm);
          cancelBtn.addEventListener('click', handleCancel);
          inputEl.addEventListener('keypress', handleKeypress);
          
          // 点击背景关闭
          modal.addEventListener('click', (e) => {
            if (e.target === modal) {
              handleCancel();
            }
          });
        });
      }
      
      // 消息提示函数
      function showMessage(message, type = 'info', duration = 3000) {
        const existingMessage = document.getElementById('message-toast');
        if (existingMessage) {
          existingMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.id = 'message-toast';
        messageDiv.className = \`message-toast message-\${type}\`;
        
        const icons = {
          success: '✓',
          error: '✕',
          warning: '⚠',
          info: 'ℹ'
        };
        
        messageDiv.innerHTML = \`
          <div class="message-content">
            <span class="message-icon">\${icons[type] || icons.info}</span>
            <span class="message-text">\${message}</span>
            <button class="message-close" onclick="this.parentElement.parentElement.remove()">×</button>
          </div>
        \`;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
          messageDiv.classList.add('message-show');
        }, 10);

        if (duration > 0) {
          setTimeout(() => {
            messageDiv.classList.add('message-hide');
            setTimeout(() => {
              if (messageDiv.parentElement) {
                messageDiv.remove();
              }
            }, 300);
          }, duration);
        }

        return messageDiv;
      }

      // 格式化日期
      function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      }

      // 加载用户数据
      function loadUsers() {
        console.log('Loading users...');
        
        // 显示加载状态
        const loadingState = document.getElementById('loadingState');
        const usersListContainer = document.getElementById('usersListContainer');
        
        if (loadingState) loadingState.classList.remove('hidden');
        if (usersListContainer) usersListContainer.classList.add('hidden');

        fetch('/api/admin/users', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include'
        })
        .then(response => {
          console.log('Response status:', response.status);
          if (!response.ok) {
            throw new Error(\`HTTP error! status: \${response.status}\`);
          }
          return response.json();
        })
        .then(data => {
          console.log('Users data received:', data);
          
          if (data.success) {
            displayUsers(data.users);
            updateStatistics(data.statistics);
          } else {
            console.error('API returned error:', data.message);
            showMessage(data.message || '获取用户列表失败', 'error');
          }
        })
        .catch(error => {
          console.error('加载用户列表错误:', error);
          showMessage(\`网络错误：\${error.message}\`, 'error');
        })
        .finally(() => {
          // 隐藏加载状态
          if (loadingState) loadingState.classList.add('hidden');
          if (usersListContainer) usersListContainer.classList.remove('hidden');
        });
      }

      // 格式化用户ID
      function formatUserId(id) {
        return String(id).padStart(6, '0');
      }

      // 显示用户列表
      function displayUsers(users) {
        console.log('Displaying users:', users);
        const usersList = document.getElementById('usersList');
        const emptyState = document.getElementById('emptyState');
        
        if (!usersList) {
          console.error('usersList element not found');
          return;
        }
        
        if (!users || users.length === 0) {
          usersList.innerHTML = '';
          if (emptyState) emptyState.classList.remove('hidden');
          return;
        }

        if (emptyState) emptyState.classList.add('hidden');
        
        usersList.innerHTML = users.map(user => \`
          <li class="px-4 py-4 hover:bg-gray-50">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                    <span class="text-gray-600 font-medium">\${user.email.charAt(0).toUpperCase()}</span>
                  </div>
                </div>
                <div class="ml-4">
                  <div class="flex items-center space-x-2">
                    <span class="text-sm font-medium text-gray-900">\${user.email}</span>
                    \${user.is_banned ? '<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">已封禁</span>' : ''}
                  </div>
                  <div class="text-sm text-gray-500">ID: \${formatUserId(user.id)}</div>
                </div>
              </div>
              <div class="flex items-center space-x-4">
                <div class="text-right">
                  <div class="text-sm text-gray-900">注册时间</div>
                  <div class="text-sm text-gray-500">\${formatDate(user.created_at)}</div>
                </div>
                <div class="flex space-x-2">
                  \${user.is_banned ? 
                    \`<button onclick="unbanUser(\${user.id})" class="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                      解封
                    </button>\` : 
                    \`<button onclick="banUser(\${user.id})" class="px-3 py-1 text-xs bg-yellow-600 text-white rounded hover:bg-yellow-700 transition-colors">
                      封禁
                    </button>\`
                  }
                  <button onclick="deleteUser(\${user.id})" class="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                    删除
                  </button>
                </div>
              </div>
            </div>
          </li>
        \`).join('');
      }

      // 更新统计信息
      function updateStatistics(statistics) {
        console.log('Updating statistics:', statistics);
        if (statistics) {
          const totalUsersEl = document.getElementById('totalUsers');
          const todayUsersEl = document.getElementById('todayUsers');
          const monthUsersEl = document.getElementById('monthUsers');
          
          if (totalUsersEl) totalUsersEl.textContent = statistics.total || 0;
          if (todayUsersEl) todayUsersEl.textContent = statistics.today || 0;
          if (monthUsersEl) monthUsersEl.textContent = statistics.month || 0;
        }
      }

      // 退出登录
      function handleLogout() {
        showConfirm('确认退出', '确定要退出登录吗？', () => {
          fetch('/api/admin/logout', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            }
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showMessage('已退出登录', 'success', 1000);
              setTimeout(() => {
                window.location.href = '/admin-login';
              }, 1000);
            } else {
              showMessage('退出登录失败', 'error');
            }
          })
          .catch(error => {
            console.error('退出登录错误:', error);
            // 即使API失败也跳转到登录页面
            window.location.href = '/admin-login';
          });
        });
      }

      // 显示管理员设置对话框
      function showAdminSettings() {
        const modal = document.getElementById('adminSettingsModal');
        if (modal) {
          modal.classList.remove('hidden');
          // 清空表单
          document.getElementById('newUsername').value = '';
          document.getElementById('currentPassword').value = '';
          document.getElementById('newPassword').value = '';
          document.getElementById('confirmPassword').value = '';
        }
      }

      // 隐藏管理员设置对话框
      function hideAdminSettings() {
        const modal = document.getElementById('adminSettingsModal');
        if (modal) {
          modal.classList.add('hidden');
        }
      }

      // 处理管理员信息修改
      function handleAdminSettingsSubmit(event) {
        event.preventDefault();
        
        const newUsername = document.getElementById('newUsername').value.trim();
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        // 表单验证
        if (!newUsername || !currentPassword || !newPassword || !confirmPassword) {
          showMessage('请填写所有字段', 'warning');
          return;
        }

        if (newPassword.length < 6) {
          showMessage('新密码长度至少6位', 'warning');
          return;
        }

        if (newPassword !== confirmPassword) {
          showMessage('两次输入的新密码不一致', 'warning');
          return;
        }

        // 显示加载状态
        const saveBtn = document.getElementById('saveBtn');
        const originalText = saveBtn.textContent;
        saveBtn.textContent = '保存中...';
        saveBtn.disabled = true;

        const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在保存修改...', 'info', 0);

        // 调用API
        fetch('/api/admin/update-credentials', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            newUsername,
            currentPassword,
            newPassword
          })
        })
        .then(response => response.json())
        .then(data => {
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }

          if (data.success) {
            showMessage('管理员信息修改成功！请重新登录', 'success', 2000);
            setTimeout(() => {
              // 修改成功后跳转到登录页面
              window.location.href = '/admin-login';
            }, 2000);
          } else {
            showMessage(data.message || '修改失败', 'error');
          }
        })
        .catch(error => {
          console.error('修改管理员信息错误:', error);
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }
          showMessage('网络错误，请稍后重试', 'error');
        })
        .finally(() => {
          // 恢复按钮状态
          saveBtn.textContent = originalText;
          saveBtn.disabled = false;
        });
      }

      // 删除用户
      function deleteUser(userId) {
        showConfirm('确认删除', '确定要删除这个用户吗？此操作不可撤销！', () => {
          const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在删除用户...', 'info', 0);

          fetch('/api/admin/delete-user', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({ userId })
          })
          .then(response => {
            console.log('Delete response status:', response.status);
            if (!response.ok) {
              throw new Error(\`HTTP error! status: \${response.status}\`);
            }
            return response.json();
          })
          .then(data => {
            console.log('Delete response data:', data);
            if (loadingMessage && loadingMessage.parentElement) {
              loadingMessage.remove();
            }

            if (data.success) {
              showMessage('用户删除成功', 'success');
              loadUsers(); // 重新加载用户列表
            } else {
              showMessage(data.message || '删除用户失败', 'error');
            }
          })
          .catch(error => {
            console.error('删除用户错误:', error);
            if (loadingMessage && loadingMessage.parentElement) {
              loadingMessage.remove();
            }
            showMessage(\`网络错误：\${error.message}\`, 'error');
          });
        });
      }

      // 封禁用户
      async function banUser(userId) {
        const reason = await showPrompt('封禁用户', '请输入封禁理由（可选）:', '违反使用条款');
        
        if (reason === null) {
          return; // 用户取消了输入
        }

        const confirmed = await showConfirm('确认封禁', \`确定要封禁这个用户吗？\\n封禁理由：\${reason || '违反使用条款'}\`);
        
        if (!confirmed) {
          return;
        }

        const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在封禁用户...', 'info', 0);

        fetch('/api/admin/ban-user', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ userId, reason: reason || '违反使用条款' })
        })
        .then(response => {
          console.log('Ban response status:', response.status);
          if (!response.ok) {
            throw new Error(\`HTTP error! status: \${response.status}\`);
          }
          return response.json();
        })
        .then(data => {
          console.log('Ban response data:', data);
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }

          if (data.success) {
            showMessage('用户封禁成功', 'success');
            loadUsers(); // 重新加载用户列表
          } else {
            showMessage(data.message || '封禁用户失败', 'error');
          }
        })
        .catch(error => {
          console.error('封禁用户错误:', error);
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }
          showMessage(\`网络错误：\${error.message}\`, 'error');
        });
      }

      // 解封用户
      function unbanUser(userId) {
        showConfirm('确认解封', '确定要解封这个用户吗？', () => {
          const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在解封用户...', 'info', 0);

          fetch('/api/admin/unban-user', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({ userId })
          })
          .then(response => {
            console.log('Unban response status:', response.status);
            if (!response.ok) {
              throw new Error(\`HTTP error! status: \${response.status}\`);
            }
            return response.json();
          })
          .then(data => {
            console.log('Unban response data:', data);
            if (loadingMessage && loadingMessage.parentElement) {
              loadingMessage.remove();
            }

            if (data.success) {
              showMessage('用户解封成功', 'success');
              loadUsers(); // 重新加载用户列表
            } else {
              showMessage(data.message || '解封用户失败', 'error');
            }
          })
          .catch(error => {
            console.error('解封用户错误:', error);
            if (loadingMessage && loadingMessage.parentElement) {
              loadingMessage.remove();
            }
            showMessage(\`网络错误：\${error.message}\`, 'error');
          });
        });
      }

      // 将函数暴露到全局作用域
      window.deleteUser = deleteUser;
      window.banUser = banUser;
      window.unbanUser = unbanUser;

      // DOM加载完成后初始化
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing admin page...');
        
        // 绑定刷新按钮
        const refreshBtn = document.getElementById('refreshBtn');
        if (refreshBtn) {
          refreshBtn.addEventListener('click', function(e) {
            e.preventDefault();
            loadUsers();
          });
        }

        // 绑定退出登录按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
          logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleLogout();
          });
        }

        // 绑定管理员信息按钮
        const adminInfoBtn = document.getElementById('adminInfoBtn');
        if (adminInfoBtn) {
          adminInfoBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAdminSettings();
          });
        }

        // 绑定关闭对话框按钮
        const closeModalBtn = document.getElementById('closeModalBtn');
        if (closeModalBtn) {
          closeModalBtn.addEventListener('click', function(e) {
            e.preventDefault();
            hideAdminSettings();
          });
        }

        // 绑定取消按钮
        const cancelBtn = document.getElementById('cancelBtn');
        if (cancelBtn) {
          cancelBtn.addEventListener('click', function(e) {
            e.preventDefault();
            hideAdminSettings();
          });
        }

        // 绑定管理员设置表单提交
        const adminSettingsForm = document.getElementById('adminSettingsForm');
        if (adminSettingsForm) {
          adminSettingsForm.addEventListener('submit', handleAdminSettingsSubmit);
        }

        // 点击对话框外部关闭
        const adminSettingsModal = document.getElementById('adminSettingsModal');
        if (adminSettingsModal) {
          adminSettingsModal.addEventListener('click', function(e) {
            if (e.target === adminSettingsModal) {
              hideAdminSettings();
            }
          });
        }

        // 初始加载用户数据
        console.log('Starting initial user data load...');
        loadUsers();
        
        console.log('Admin page initialized successfully');
      });
    </script>
    
    <script type="module">
      import { motion as framerMotion } from 'https://esm.sh/framer-motion@11.0.24';
      
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Framer Motion loaded for admin page');
        
        // 初始化页面动画
        const mainContent = document.querySelector('.max-w-7xl');
        if (mainContent) {
          framerMotion(mainContent, {
            initial: { opacity: 0, y: 20 },
            animate: { opacity: 1, y: 0 },
            transition: { duration: 0.5 }
          });
        }
      });
    </script>
  `;
  
  return renderHTMLTemplate('管理员面板', content, true);
}