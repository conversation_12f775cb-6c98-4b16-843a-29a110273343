# Assets 资源文件夹

此文件夹用于存放项目的静态资源文件。

## 文件夹结构

- `images/` - 通用图片文件
- `icons/` - 图标文件（favicon.ico, apple-touch-icon.png 等）
- `logos/` - Logo文件（不同尺寸和格式）

## 使用说明

### Favicon
将favicon相关文件放置在 `icons/` 文件夹中：
- `favicon.ico` - 标准favicon
- `favicon-16x16.png` - 16x16像素PNG格式
- `favicon-32x32.png` - 32x32像素PNG格式
- `apple-touch-icon.png` - Apple设备图标（180x180）

### Logo
将Logo文件放置在 `logos/` 文件夹中：
- `logo.svg` - 矢量格式Logo
- `logo.png` - PNG格式Logo
- `logo-dark.svg` - 深色主题Logo
- `logo-light.svg` - 浅色主题Logo

### 在代码中引用
在Cloudflare Worker中，这些资源需要通过路由处理来提供服务。