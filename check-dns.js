// DNS 检查工具
async function checkDNSRecords() {
  console.log('检查 conef.top 的邮件相关 DNS 记录...\n');
  
  const domain = 'conef.top';
  const records = [
    { name: `${domain}`, type: 'TXT', description: 'SPF 记录' },
    { name: `_dmarc.${domain}`, type: 'TXT', description: 'DMARC 记录' },
    { name: `mailchannels._domainkey.${domain}`, type: 'TXT', description: 'DKIM 记录' }
  ];
  
  for (const record of records) {
    try {
      const response = await fetch(`https://dns.google/resolve?name=${record.name}&type=${record.type}`);
      const data = await response.json();
      
      console.log(`📧 ${record.description}:`);
      console.log(`   查询: ${record.name}`);
      
      if (data.Answer && data.Answer.length > 0) {
        data.Answer.forEach(answer => {
          if (answer.data.includes('spf1') || answer.data.includes('DMARC1') || answer.data.includes('DKIM1')) {
            console.log(`   ✅ 找到: ${answer.data}`);
          }
        });
      } else {
        console.log(`   ❌ 未找到记录`);
      }
      console.log('');
    } catch (error) {
      console.error(`   ❌ 查询失败: ${error.message}\n`);
    }
  }
}

checkDNSRecords();