import { renderHTMLTemplate } from '../utils/template';

export function getAdminLoginPage(): string {
  const content = `
    <div class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
      <div id="form-container" class="form-container">
        <div id="adminLogin" class="text-center">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">管理员登录</h2>
          <form class="space-y-6">
            <div class="input-container">
              <input 
                id="adminUsername"
                type="text" 
                placeholder="管理员账号" 
                class="w-full px-4 py-4 rounded-full text-center text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
              <label class="input-label">管理员账号</label>
            </div>
            <div class="input-container">
              <input 
                id="adminPassword"
                type="password" 
                placeholder="密码" 
                class="w-full px-4 py-4 rounded-full text-center text-gray-600 focus:outline-none transition-all duration-200"
                required
              >
              <label class="input-label">密码</label>
            </div>
            <button type="button" id="adminLoginBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              登录
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            <a href="/" class="text-blue-600 hover:underline">返回首页</a>
          </p>
        </div>
      </div>
    </div>

    <script>
      console.log('Admin login page script loading...');
      
      // 消息提示函数
      function showMessage(message, type = 'info', duration = 3000) {
        const existingMessage = document.getElementById('message-toast');
        if (existingMessage) {
          existingMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.id = 'message-toast';
        messageDiv.className = \`message-toast message-\${type}\`;
        
        const icons = {
          success: '✓',
          error: '✕',
          warning: '⚠',
          info: 'ℹ'
        };
        
        messageDiv.innerHTML = \`
          <div class="message-content">
            <span class="message-icon">\${icons[type] || icons.info}</span>
            <span class="message-text">\${message}</span>
            <button class="message-close" onclick="this.parentElement.parentElement.remove()">×</button>
          </div>
        \`;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
          messageDiv.classList.add('message-show');
        }, 10);

        if (duration > 0) {
          setTimeout(() => {
            messageDiv.classList.add('message-hide');
            setTimeout(() => {
              if (messageDiv.parentElement) {
                messageDiv.remove();
              }
            }, 300);
          }, duration);
        }

        return messageDiv;
      }
      
      function handleAdminLogin() {
        console.log('Handling admin login');
        const username = document.getElementById('adminUsername').value;
        const password = document.getElementById('adminPassword').value;
        
        if (!username || !password) {
          showMessage('请输入管理员账号和密码', 'warning');
          return;
        }

        // 显示加载状态
        const submitBtn = document.getElementById('adminLoginBtn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '登录中...';
        submitBtn.disabled = true;

        const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在验证管理员身份...', 'info', 0);

        // 调用管理员登录API
        fetch('/api/admin/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ username, password })
        })
        .then(response => response.json())
        .then(data => {
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }

          if (data.success) {
            showMessage('管理员登录成功！即将跳转到管理页面', 'success', 1500);
            setTimeout(() => {
              window.location.href = '/admin';
            }, 1500);
          } else {
            showMessage(data.message || '登录失败，请检查账号和密码', 'error');
          }
        })
        .catch(error => {
          console.error('管理员登录错误:', error);
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }
          showMessage('网络错误，请稍后重试', 'error');
        })
        .finally(() => {
          // 恢复按钮状态
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        });
      }
      
      // DOM加载完成后初始化
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, binding events...');
        
        // 绑定管理员登录按钮事件
        const adminLoginBtn = document.getElementById('adminLoginBtn');
        if (adminLoginBtn) {
          adminLoginBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Admin login button clicked');
            handleAdminLogin();
          });
        }
        
        // 支持回车键登录
        document.addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            handleAdminLogin();
          }
        });
        
        console.log('Admin login events bound successfully');
      });
    </script>
    
    <script type="module">
      import { motion as framerMotion } from 'https://esm.sh/framer-motion@11.0.24';
      
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Framer Motion loaded for admin login');
        
        // 初始化动画
        const formContainer = document.getElementById('form-container');
        if (formContainer) {
          framerMotion(formContainer, {
            initial: { opacity: 0, scale: 0.95 },
            animate: { opacity: 1, scale: 1 },
            transition: { duration: 0.5 }
          });
        }
      });
    </script>
  `;
  
  return renderHTMLTemplate('管理员登录', content, true);
}