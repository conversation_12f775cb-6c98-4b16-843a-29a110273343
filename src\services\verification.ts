export interface VerificationCode {
  id: number;
  email: string;
  code: string;
  type: 'register' | 'reset_password';
  created_at: string;
  expires_at: string;
  used: boolean;
  used_at?: string;
}

export class VerificationService {
  private db: D1Database;

  constructor(db: D1Database) {
    this.db = db;
  }

  private generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async generateAndStore(email: string, type: 'register' | 'reset_password' = 'register'): Promise<{ success: boolean; message: string; code?: string }> {
    try {
      const code = this.generateCode();
      const expiresAt = new Date();
      expiresAt.setMinutes(expiresAt.getMinutes() + 10);

      await this.db.prepare(`
        UPDATE verification_codes 
        SET used = TRUE, used_at = datetime('now') 
        WHERE email = ? AND type = ? AND used = FALSE
      `).bind(email, type).run();

      const result = await this.db.prepare(`
        INSERT INTO verification_codes (email, code, type, expires_at) 
        VALUES (?, ?, ?, ?)
      `).bind(email, code, type, expiresAt.toISOString()).run();

      if (result.success) {
        return { 
          success: true, 
          message: '验证码生成成功', 
          code 
        };
      } else {
        return { success: false, message: '验证码生成失败' };
      }
    } catch (error) {
      console.error('生成验证码错误:', error);
      return { success: false, message: '服务器错误，请稍后重试' };
    }
  }

  async verify(email: string, code: string, type: 'register' | 'reset_password' = 'register'): Promise<{ success: boolean; message: string }> {
    try {
      const verificationCode = await this.db.prepare(`
        SELECT * FROM verification_codes 
        WHERE email = ? AND code = ? AND type = ? AND used = FALSE AND expires_at > datetime('now')
        ORDER BY created_at DESC 
        LIMIT 1
      `).bind(email, code, type).first() as VerificationCode;

      if (!verificationCode) {
        return { success: false, message: '验证码无效或已过期' };
      }

      await this.db.prepare(`
        UPDATE verification_codes 
        SET used = TRUE, used_at = datetime('now') 
        WHERE id = ?
      `).bind(verificationCode.id).run();

      return { success: true, message: '验证码验证成功' };
    } catch (error) {
      console.error('验证码验证错误:', error);
      return { success: false, message: '服务器错误，请稍后重试' };
    }
  }

  async checkRateLimit(email: string, type: 'register' | 'reset_password' = 'register'): Promise<{ allowed: boolean; message: string }> {
    try {
      const oneMinuteAgo = new Date();
      oneMinuteAgo.setMinutes(oneMinuteAgo.getMinutes() - 1);

      const recentCode = await this.db.prepare(`
        SELECT COUNT(*) as count FROM verification_codes 
        WHERE email = ? AND type = ? AND created_at > ?
      `).bind(email, type, oneMinuteAgo.toISOString()).first() as { count: number };

      if (recentCode.count >= 1) {
        return { allowed: false, message: '请等待1分钟后再次发送验证码' };
      }

      return { allowed: true, message: '可以发送验证码' };
    } catch (error) {
      console.error('频率限制检查错误:', error);
      return { allowed: false, message: '服务器错误，请稍后重试' };
    }
  }

  async sendEmail(email: string, code: string, type: 'register' | 'reset_password' = 'register', resendApiKey?: string): Promise<{ success: boolean; message: string }> {
    try {
      const subject = type === 'register' ? '注册验证码' : '密码重置验证码';
      const htmlBody = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; text-align: center; margin-bottom: 30px;">ConeF - ${subject}</h2>
            <div style="text-align: center; margin: 30px 0;">
              <div style="display: inline-block; background-color: #f0f0f0; padding: 20px 30px; border-radius: 5px; font-size: 24px; font-weight: bold; letter-spacing: 3px; color: #333;">
                ${code}
              </div>
            </div>
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              您的验证码是: <strong>${code}</strong>
            </p>
            <p style="color: #666; line-height: 1.6; margin-bottom: 20px;">
              验证码有效期为10分钟，请及时使用。
            </p>
            <p style="color: #999; font-size: 14px; margin-top: 30px;">
              如果这不是您的操作，请忽略此邮件。
            </p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            <p style="color: #999; font-size: 12px; text-align: center;">
              此邮件由 ConeF 系统自动发送，请勿回复。<br>
              访问 <a href="https://conef.top" style="color: #3b82f6;">conef.top</a>
            </p>
          </div>
        </div>
      `;

      const textBody = `
        ConeF - ${subject}
        
        您的验证码是: ${code}
        
        验证码有效期为10分钟，请及时使用。
        
        如果这不是您的操作，请忽略此邮件。
        
        ---
        此邮件由 ConeF 系统自动发送，请勿回复。
        访问 conef.top
      `;

      // 检查是否有 Resend API Key
      if (!resendApiKey) {
        // 开发环境：记录邮件内容到控制台
        console.log('='.repeat(50));
        console.log('📧 邮件发送模拟 (开发环境 - 无API Key)');
        console.log('='.repeat(50));
        console.log('收件人:', email);
        console.log('发件人: ConeF <<EMAIL>>');
        console.log('主题:', subject);
        console.log('验证码:', code);
        console.log('='.repeat(50));
        console.log('邮件内容:');
        console.log(textBody);
        console.log('='.repeat(50));
        
        return { success: true, message: '验证码已发送（开发模式）' };
      }

      // 使用 Resend 发送邮件
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${resendApiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          from: 'ConeF <<EMAIL>>',
          to: [email],
          subject,
          html: htmlBody,
          text: textBody
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('邮件发送成功:', result);
        return { success: true, message: '验证码已发送' };
      } else {
        const errorText = await response.text();
        console.error('邮件发送失败 - 状态码:', response.status);
        console.error('邮件发送失败 - 响应:', errorText);
        return { success: false, message: `邮件发送失败: ${response.status}` };
      }
    } catch (error) {
      console.error('邮件发送错误:', error);
      return { success: false, message: '邮件发送失败，请稍后重试' };
    }
  }

  async cleanupExpired(): Promise<void> {
    try {
      await this.db.prepare(`
        DELETE FROM verification_codes 
        WHERE expires_at < datetime('now') OR (used = TRUE AND used_at < datetime('now', '-1 day'))
      `).run();
    } catch (error) {
      console.error('清理过期验证码错误:', error);
    }
  }
}