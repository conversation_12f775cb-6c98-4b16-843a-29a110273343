import { User, Session } from '../types/auth';

export class AuthService {
  private db: D1Database;

  constructor(db: D1Database) {
    this.db = db;
  }

  // 哈希密码 (简单实现，生产环境建议使用更强的哈希算法)
  private async hashPassword(password: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(password);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  // 验证密码
  private async verifyPassword(password: string, hash: string): Promise<boolean> {
    const passwordHash = await this.hashPassword(password);
    return passwordHash === hash;
  }

  // 生成会话ID
  private generateSessionId(): string {
    return crypto.randomUUID();
  }

  // 用户注册
  async register(email: string, password: string): Promise<{ success: boolean; message: string; user?: User }> {
    try {
      // 检查用户是否已存在
      const existingUser = await this.db.prepare('SELECT id FROM users WHERE email = ?').bind(email).first();
      if (existingUser) {
        return { success: false, message: '该邮箱已被注册' };
      }

      // 哈希密码
      const passwordHash = await this.hashPassword(password);

      // 插入新用户
      const result = await this.db.prepare(`
        INSERT INTO users (email, password_hash, created_at, updated_at) 
        VALUES (?, ?, datetime('now'), datetime('now'))
      `).bind(email, passwordHash).run();

      if (result.success) {
        const user = await this.db.prepare('SELECT id, email, created_at, updated_at FROM users WHERE id = ?')
          .bind(result.meta.last_row_id).first() as User;
        
        return { 
          success: true, 
          message: '注册成功', 
          user 
        };
      } else {
        return { success: false, message: '注册失败，请稍后重试' };
      }
    } catch (error) {
      console.error('注册错误:', error);
      return { success: false, message: '服务器错误，请稍后重试' };
    }
  }

  // 用户登录
  async login(email: string, password: string): Promise<{ success: boolean; message: string; user?: User; sessionId?: string }> {
    try {
      // 查找用户 - 使用COALESCE处理可能不存在的封禁字段
      const user = await this.db.prepare(`
        SELECT id, email, password_hash, 
               COALESCE(is_banned, 0) as is_banned, 
               banned_at, banned_reason, 
               created_at, updated_at 
        FROM users WHERE email = ?
      `).bind(email).first() as User;
      
      if (!user) {
        return { success: false, message: '邮箱或密码错误' };
      }

      // 检查用户是否被封禁
      if (user.is_banned) {
        const banReason = user.banned_reason || '违反使用条款';
        return { success: false, message: `账号已被封禁，原因：${banReason}` };
      }

      // 验证密码
      const isValidPassword = await this.verifyPassword(password, user.password_hash);
      if (!isValidPassword) {
        return { success: false, message: '邮箱或密码错误' };
      }

      // 创建会话
      const sessionId = this.generateSessionId();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7); // 7天后过期

      await this.db.prepare(`
        INSERT INTO sessions (id, user_id, created_at, expires_at) 
        VALUES (?, ?, datetime('now'), ?)
      `).bind(sessionId, user.id, expiresAt.toISOString()).run();

      return { 
        success: true, 
        message: '登录成功',
        user: {
          id: user.id,
          email: user.email,
          password_hash: user.password_hash,
          created_at: user.created_at,
          updated_at: user.updated_at
        },
        sessionId 
      };
    } catch (error) {
      console.error('登录错误:', error);
      return { success: false, message: '服务器错误，请稍后重试' };
    }
  }

  // 验证会话
  async validateSession(sessionId: string): Promise<{ valid: boolean; user?: User }> {
    try {
      const session = await this.db.prepare(`
        SELECT s.*, u.id, u.email, u.created_at as user_created_at, u.updated_at as user_updated_at
        FROM sessions s 
        JOIN users u ON s.user_id = u.id 
        WHERE s.id = ? AND s.expires_at > datetime('now')
      `).bind(sessionId).first();

      if (session) {
        return {
          valid: true,
          user: {
            id: session.id,
            email: session.email,
            password_hash: '',
            created_at: session.user_created_at,
            updated_at: session.user_updated_at
          }
        };
      }

      return { valid: false };
    } catch (error) {
      console.error('会话验证错误:', error);
      return { valid: false };
    }
  }

  // 登出 (删除会话)
  async logout(sessionId: string): Promise<boolean> {
    try {
      await this.db.prepare('DELETE FROM sessions WHERE id = ?').bind(sessionId).run();
      return true;
    } catch (error) {
      console.error('登出错误:', error);
      return false;
    }
  }
}