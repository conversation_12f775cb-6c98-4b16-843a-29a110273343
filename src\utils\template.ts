export function renderHTMLTemplate(title: string, content: string, includeFramerMotion: boolean = false): string {
  const framerMotionScript = includeFramerMotion ? `
    <script type="module">
      import { animate, motion, AnimatePresence } from 'https://esm.sh/framer-motion@12.23.11';
      window.animate = animate;
      window.motion = motion;
      window.AnimatePresence = AnimatePresence;
    </script>
  ` : '';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      
      <!-- Favicon -->
      <link rel="icon" type="image/x-icon" href="/favicon.ico">
      <link rel="icon" type="image/png" sizes="16x16" href="/assets/icons/favicon-16x16.png">
      <link rel="icon" type="image/png" sizes="32x32" href="/assets/icons/favicon-32x32.png">
      <link rel="apple-touch-icon" sizes="180x180" href="/assets/icons/apple-touch-icon.png">
      
      <script src="https://cdn.tailwindcss.com"></script>
      <link rel="stylesheet" href="/styles.css">
      ${framerMotionScript}
    </head>
    <body>
      ${content}
    </body>
    </html>
  `;
}