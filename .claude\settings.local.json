{"permissions": {"allow": ["Bash(npx wrangler d1 create conef_user)", "Bash(npm install:*)", "Bash(npx tailwindcss:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "Bash(npx wrangler d1 execute:*)", "Bash(node:*)", "Bash(npm run:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(wrangler secret:*)", "<PERSON><PERSON>(curl:*)", "<PERSON>sh(wrangler d1 execute:*)", "Bash(wrangler tail:*)", "<PERSON><PERSON>(npx wrangler secret:*)", "Bash(npx wrangler deploy --dry-run)", "Bash(mkdir assets)", "Bash(mkdir images icons logos)"], "deny": []}}