// 测试 Resend 邮件发送
async function testEmailSending() {
  try {
    console.log('正在测试 Resend 邮件发送...');
    
    const response = await fetch('http://127.0.0.1:14400/api/send-verification-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>'
      })
    });
    
    const result = await response.json();
    console.log('响应状态:', response.status);
    console.log('响应结果:', result);
    
    if (result.success) {
      console.log('✅ 验证码邮件发送成功！请检查 <EMAIL> 的邮箱');
    } else {
      console.log('❌ 验证码邮件发送失败:', result.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testEmailSending();