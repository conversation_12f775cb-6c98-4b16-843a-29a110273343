import { renderHTMLTemplate } from '../utils/template';

export function getRegisterPage(): string {
  const content = `
    <style>
      .form-container {
        max-width: 400px;
        width: 100%;
        background: white;
        padding: 2rem;
        border-radius: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: height 0.3s ease-in-out;
      }
      
      .smooth-transition {
        transition: all 0.3s ease-in-out;
      }
    </style>
    
    <div class="bg-gray-50 min-h-screen flex items-center justify-center p-4">
      <div id="form-container" class="form-container">
        
        <!-- 初始注册页面 -->
        <div id="registerInitial" class="text-center">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">创建帐户</h2>
          <form class="space-y-6">
            <div class="relative">
              <input 
                id="registerEmail"
                type="email" 
                placeholder="电子邮件地址" 
                class="w-full px-4 py-4 border border-gray-300 rounded-full text-center text-gray-600 focus:outline-none focus:border-blue-500 transition-all duration-200"
                required
              >
              <label class="input-label">电子邮件地址</label>
            </div>
            <div class="flex items-center border border-gray-300 rounded-full px-4 py-4 bg-gray-50">
              <input 
                id="verificationCode"
                type="text" 
                placeholder="点击发送获取验证码"
                class="flex-1 bg-transparent outline-none text-center text-gray-600"
                readonly
              >
              <button 
                type="button" 
                id="sendCodeBtn" 
                class="text-blue-600 hover:underline text-sm font-medium disabled:text-gray-400 disabled:no-underline disabled:cursor-not-allowed"
              >
                发送
              </button>
            </div>
            <button type="button" id="registerEmailBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            已有帐户？ 
            <a href="/login" class="text-blue-600 hover:underline">请登录</a>
          </p>
        </div>

        <!-- 注册密码设置页面 -->
        <div id="registerPassword" class="text-center hidden">
          <h2 class="text-2xl font-semibold text-gray-800 mb-10">创建帐户</h2>
          <form class="space-y-6">
            <div class="relative">
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>电子邮件地址</span>
              </div>
              <div class="flex items-center border border-gray-300 rounded-full px-4 py-3 bg-gray-50">
                <span id="displayRegisterEmail" class="flex-1 text-gray-700"></span>
                <button type="button" id="editRegisterEmailBtn" class="text-blue-600 hover:underline text-sm font-medium">编辑</button>
              </div>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>验证码</span>
              </div>
              <div class="relative">
                <input 
                  id="verificationCodeConfirm"
                  type="text" 
                  placeholder="输入验证码"
                  class="w-full px-4 py-4 border border-gray-300 rounded-full text-center text-gray-600 focus:outline-none focus:border-blue-500 transition-all duration-200"
                  required
                >
                <label class="input-label">验证码</label>
              </div>
            </div>
            <div>
              <div class="flex items-center justify-between text-sm text-gray-500 mb-2">
                <span>密码</span>
              </div>
              <div class="relative">
                <input 
                  id="registerPasswordInput"
                  type="password" 
                  class="w-full px-4 py-4 border border-gray-300 rounded-full text-gray-600 focus:outline-none focus:border-blue-500 transition-all duration-200"
                  required
                >
                <label class="input-label">密码</label>
              </div>
            </div>
            <button type="button" id="registerSubmitBtn" class="w-full bg-gray-900 text-white py-4 rounded-full font-medium hover:bg-gray-800 transition-colors">
              继续
            </button>
          </form>
          <p class="mt-8 text-gray-600">
            已有帐户？ 
            <a href="/login" class="text-blue-600 hover:underline">请登录</a>
          </p>
        </div>
      </div>
    </div>

    <script>
      console.log('Register page script loading...');
      
      // 全局变量
      let motion = null;
      let countdown = 0;
      let countdownTimer = null;
      
      // 消息提示函数
      function showMessage(message, type = 'info', duration = 3000) {
        const existingMessage = document.getElementById('message-toast');
        if (existingMessage) {
          existingMessage.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.id = 'message-toast';
        messageDiv.className = \`message-toast message-\${type}\`;
        
        const icons = {
          success: '✓',
          error: '✕',
          warning: '⚠',
          info: 'ℹ'
        };
        
        messageDiv.innerHTML = \`
          <div class="message-content">
            <span class="message-icon">\${icons[type] || icons.info}</span>
            <span class="message-text">\${message}</span>
            <button class="message-close" onclick="this.parentElement.parentElement.remove()">×</button>
          </div>
        \`;

        document.body.appendChild(messageDiv);

        setTimeout(() => {
          messageDiv.classList.add('message-show');
        }, 10);

        if (duration > 0) {
          setTimeout(() => {
            messageDiv.classList.add('message-hide');
            setTimeout(() => {
              if (messageDiv.parentElement) {
                messageDiv.remove();
              }
            }, 300);
          }, duration);
        }

        return messageDiv;
      }
      
      // 改进的表单切换函数
      async function switchForm(fromFormId, toFormId) {
        const fromForm = document.getElementById(fromFormId);
        const toForm = document.getElementById(toFormId);
        const container = document.getElementById('form-container');
        
        if (!fromForm || !toForm || !container) return;
        
        // 如果有motion，使用平滑动画
        if (motion) {
          // 1. 先让当前表单淡出
          await motion(fromForm, {
            opacity: [1, 0],
            y: [0, -20],
            transition: { duration: 0.2 }
          }).finished;
          
          // 2. 隐藏当前表单并显示新表单（但先设为透明）
          fromForm.classList.add('hidden');
          toForm.classList.remove('hidden');
          toForm.style.opacity = '0';
          
          // 3. 让容器平滑调整到新高度
          const containerHeight = container.offsetHeight;
          const newHeight = toForm.offsetHeight;
          
          if (Math.abs(containerHeight - newHeight) > 10) {
            await motion(container, {
              height: [containerHeight + 'px', newHeight + 'px'],
              transition: { duration: 0.3, ease: 'easeInOut' }
            }).finished;
          }
          
          // 4. 让新表单淡入
          toForm.style.opacity = '';
          await motion(toForm, {
            opacity: [0, 1],
            y: [20, 0],
            transition: { duration: 0.2 }
          }).finished;
          
        } else {
          // 降级方案：没有motion时的简单切换
          fromForm.classList.add('hidden');
          toForm.classList.remove('hidden');
        }
      }
      
      // 基本函数 - 简化版本
      function hideAllForms() {
        const forms = ['registerInitial', 'registerPassword'];
        forms.forEach(id => {
          const element = document.getElementById(id);
          if (element) {
            element.classList.add('hidden');
          }
        });
      }
      
      function showForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
          form.classList.remove('hidden');
          
          // 如果有motion，添加动画
          if (motion) {
            motion(form, {
              initial: { opacity: 0, y: 20 },
              animate: { opacity: 1, y: 0 },
              transition: { duration: 0.3 }
            });
          }
        }
      }
      
      function handleRegisterEmail() {
        console.log('Handling register email');
        const email = document.getElementById('registerEmail').value;
        const verificationCode = document.getElementById('verificationCode').value;
        
        if (!email) {
          showMessage('请输入邮箱地址', 'warning');
          return;
        }

        if (!verificationCode) {
          showMessage('请输入验证码', 'warning');
          return;
        }

        // 简单的邮箱格式验证
        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        if (!emailRegex.test(email)) {
          showMessage('邮箱格式不正确', 'error');
          return;
        }

        document.getElementById('displayRegisterEmail').textContent = email;
        document.getElementById('verificationCodeConfirm').value = verificationCode;
        
        // 使用新的平滑切换动画
        switchForm('registerInitial', 'registerPassword');
      }
      
      function sendVerificationCode() {
        const email = document.getElementById('registerEmail').value;
        
        if (!email) {
          showMessage('请先输入邮箱地址', 'warning');
          return;
        }

        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
        if (!emailRegex.test(email)) {
          showMessage('邮箱格式不正确', 'error');
          return;
        }

        const sendBtn = document.getElementById('sendCodeBtn');
        const codeInput = document.getElementById('verificationCode');
        
        // 显示发送中状态
        sendBtn.disabled = true;
        sendBtn.textContent = '发送中...';
        
        // 发送验证码请求
        fetch('/api/send-verification-code', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email })
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            showMessage('验证码已发送！', 'success');
            
            // 开始倒计时
            countdown = 60;
            sendBtn.textContent = countdown + 's';
            codeInput.placeholder = '请输入验证码';
            codeInput.readOnly = false;
            // 保持透明背景，不添加白色背景
            
            
            countdownTimer = setInterval(() => {
              countdown--;
              if (countdown > 0) {
                sendBtn.textContent = countdown + 's';
              } else {
                clearInterval(countdownTimer);
                sendBtn.disabled = false;
                sendBtn.textContent = '重新发送';
              }
            }, 1000);
          } else {
            showMessage(data.message || '发送失败，请稍后重试', 'error');
            sendBtn.disabled = false;
            sendBtn.textContent = '发送';
          }
        })
        .catch(error => {
          console.error('发送验证码错误:', error);
          showMessage('网络错误，请稍后重试', 'error');
          sendBtn.disabled = false;
          sendBtn.textContent = '发送';
        });
      }
      
      function editRegisterEmail() {
        // 使用新的平滑切换动画
        switchForm('registerPassword', 'registerInitial');
      }
      
      function handleRegisterSubmit() {
        console.log('Handling register submit');
        const email = document.getElementById('displayRegisterEmail').textContent;
        const password = document.getElementById('registerPasswordInput').value;
        const verificationCode = document.getElementById('verificationCodeConfirm').value;
        
        if (!password) {
          showMessage('请输入密码', 'warning');
          return;
        }

        if (!verificationCode) {
          showMessage('请输入验证码', 'warning');
          return;
        }

        if (password.length < 6) {
          showMessage('密码长度至少6位', 'warning');
          return;
        }

        // 显示加载状态
        const submitBtn = document.getElementById('registerSubmitBtn');
        const originalText = submitBtn.textContent;
        submitBtn.textContent = '注册中...';
        submitBtn.disabled = true;

        const loadingMessage = showMessage('<span class="loading-spinner"></span> 正在注册...', 'info', 0);

        // 调用注册API
        fetch('/api/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ email, password, verificationCode })
        })
        .then(response => response.json())
        .then(data => {
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }

          if (data.success) {
            showMessage('注册成功！即将跳转到登录页面', 'success', 2000);
            setTimeout(() => {
              window.location.href = '/login';
            }, 2000);
          } else {
            showMessage(data.message || '注册失败，请稍后重试', 'error');
          }
        })
        .catch(error => {
          console.error('注册错误:', error);
          // 移除加载消息
          if (loadingMessage && loadingMessage.parentElement) {
            loadingMessage.remove();
          }
          showMessage('网络错误，请稍后重试', 'error');
        })
        .finally(() => {
          // 恢复按钮状态
          submitBtn.textContent = originalText;
          submitBtn.disabled = false;
        });
      }
      
      // DOM加载完成后初始化
      document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, binding events...');
        
        // 绑定所有按钮事件
        const registerEmailBtn = document.getElementById('registerEmailBtn');
        if (registerEmailBtn) {
          registerEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Register email button clicked');
            handleRegisterEmail();
          });
        }
        
        const sendCodeBtn = document.getElementById('sendCodeBtn');
        if (sendCodeBtn) {
          sendCodeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Send code button clicked');
            sendVerificationCode();
          });
        }
        
        const editRegisterEmailBtn = document.getElementById('editRegisterEmailBtn');
        if (editRegisterEmailBtn) {
          editRegisterEmailBtn.addEventListener('click', function(e) {
            e.preventDefault();
            editRegisterEmail();
          });
        }
        
        const registerSubmitBtn = document.getElementById('registerSubmitBtn');
        if (registerSubmitBtn) {
          registerSubmitBtn.addEventListener('click', function(e) {
            e.preventDefault();
            handleRegisterSubmit();
          });
        }
        
        // 初始化显示注册表单
        showForm('registerInitial');
        
        console.log('All events bound successfully');
      });
    </script>
    
    <script type="module">
      import { motion as framerMotion } from 'https://esm.sh/framer-motion@11.0.24';
      
      // 设置全局motion变量
      motion = framerMotion;
      
      document.addEventListener('DOMContentLoaded', function() {
        console.log('Framer Motion loaded');
        
        // 初始化动画
        const formContainer = document.getElementById('form-container');
        if (formContainer) {
          framerMotion(formContainer, {
            initial: { opacity: 0, scale: 0.95 },
            animate: { opacity: 1, scale: 1 },
            transition: { duration: 0.5 }
          });
        }
      });
    </script>
  `;
  
  return renderHTMLTemplate('注册', content, true);
}