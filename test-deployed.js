// 测试部署版本的 Resend 邮件发送
async function testDeployedEmailSending() {
  try {
    console.log('正在测试部署版本的 Resend 邮件发送...');
    
    const response = await fetch('https://cloudflare-project-2.conef.workers.dev/api/send-verification-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: `test${Date.now()}@example.com`
      })
    });
    
    const result = await response.json();
    console.log('响应状态:', response.status);
    console.log('响应结果:', result);
    
    if (result.success) {
      console.log('✅ 验证码邮件发送成功！请检查 <EMAIL> 的邮箱');
      if (result.message.includes('开发模式')) {
        console.log('⚠️  仍在开发模式，API Key 可能未正确配置');
      } else {
        console.log('🎉 使用 Resend API 发送成功！');
      }
    } else {
      console.log('❌ 验证码邮件发送失败:', result.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testDeployedEmailSending();