{"name": "cloudflare-project-2", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "test": "vitest", "cf-typegen": "wrangler types", "build:css": "tailwindcss -i ./src/styles.css -o ./dist/styles.css --watch", "build:css:prod": "tailwindcss -i ./src/styles.css -o ./dist/styles.css --minify"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.19", "tailwindcss": "^4.1.11", "typescript": "^5.5.2", "vitest": "~3.2.0", "wrangler": "^4.26.1"}, "dependencies": {"lucide": "^0.534.0", "resend": "^4.7.0"}}