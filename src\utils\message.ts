// 消息提示工具类
export class MessageUtil {
  static showMessage(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', duration: number = 3000) {
    // 移除已存在的消息
    const existingMessage = document.getElementById('message-toast');
    if (existingMessage) {
      existingMessage.remove();
    }

    // 创建消息容器
    const messageDiv = document.createElement('div');
    messageDiv.id = 'message-toast';
    messageDiv.className = `message-toast message-${type}`;
    
    // 设置消息内容
    messageDiv.innerHTML = `
      <div class="message-content">
        <span class="message-icon">${this.getIcon(type)}</span>
        <span class="message-text">${message}</span>
        <button class="message-close" onclick="this.parentElement.parentElement.remove()">×</button>
      </div>
    `;

    // 添加到页面
    document.body.appendChild(messageDiv);

    // 添加显示动画
    setTimeout(() => {
      messageDiv.classList.add('message-show');
    }, 10);

    // 自动隐藏
    if (duration > 0) {
      setTimeout(() => {
        this.hideMessage(messageDiv);
      }, duration);
    }

    return messageDiv;
  }

  static getIcon(type: string): string {
    switch (type) {
      case 'success': return '✓';
      case 'error': return '✕';
      case 'warning': return '⚠';
      case 'info': return 'ℹ';
      default: return 'ℹ';
    }
  }

  static hideMessage(messageDiv: HTMLElement) {
    messageDiv.classList.add('message-hide');
    setTimeout(() => {
      if (messageDiv.parentElement) {
        messageDiv.remove();
      }
    }, 300);
  }

  static showSuccess(message: string, duration?: number) {
    return this.showMessage(message, 'success', duration);
  }

  static showError(message: string, duration?: number) {
    return this.showMessage(message, 'error', duration);
  }

  static showWarning(message: string, duration?: number) {
    return this.showMessage(message, 'warning', duration);
  }

  static showInfo(message: string, duration?: number) {
    return this.showMessage(message, 'info', duration);
  }

  // 显示加载消息（不自动消失）
  static showLoading(message: string = '加载中...') {
    return this.showMessage(`<span class="loading-spinner"></span> ${message}`, 'info', 0);
  }
}

// 全局函数，供HTML页面使用
declare global {
  interface Window {
    showMessage: (message: string, type?: 'success' | 'error' | 'warning' | 'info', duration?: number) => HTMLElement;
    showSuccess: (message: string, duration?: number) => HTMLElement;
    showError: (message: string, duration?: number) => HTMLElement;
    showWarning: (message: string, duration?: number) => HTMLElement;
    showInfo: (message: string, duration?: number) => HTMLElement;
    showLoading: (message?: string) => HTMLElement;
    hideMessage: (messageDiv: HTMLElement) => void;
  }
}

// 将工具函数挂载到全局
if (typeof window !== 'undefined') {
  window.showMessage = MessageUtil.showMessage.bind(MessageUtil);
  window.showSuccess = MessageUtil.showSuccess.bind(MessageUtil);
  window.showError = MessageUtil.showError.bind(MessageUtil);
  window.showWarning = MessageUtil.showWarning.bind(MessageUtil);
  window.showInfo = MessageUtil.showInfo.bind(MessageUtil);
  window.showLoading = MessageUtil.showLoading.bind(MessageUtil);
  window.hideMessage = MessageUtil.hideMessage.bind(MessageUtil);
}