// 图标管理工具 - 统一管理所有Lucide图标的导入和初始化
export class IconManager {
  private static icons: any = {};
  private static createIcons: any = null;
  
  // 初始化图标库
  static async initialize() {
    try {
      const lucide = await import('https://esm.sh/lucide@latest');
      this.createIcons = lucide.createIcons;
      
      // 导入所有需要的图标
      this.icons = {
        Home: lucide.Home,
        User: lucide.User,
        LogOut: lucide.LogOut,
        Mail: lucide.Mail,
        Lock: lucide.Lock,
        Eye: lucide.Eye,
        EyeOff: lucide.EyeOff,
        Check: lucide.Check,
        X: lucide.X,
        AlertCircle: lucide.AlertCircle,
        Info: lucide.Info
      };
      
      return true;
    } catch (error) {
      console.error('Failed to load Lucide icons:', error);
      return false;
    }
  }
  
  // 创建图标实例
  static create(iconNames: string[] = []) {
    if (!this.createIcons) {
      console.warn('IconManager not initialized');
      return;
    }
    
    const iconsToCreate: any = {};
    
    // 如果没有指定图标，则创建所有已导入的图标
    if (iconNames.length === 0) {
      Object.assign(iconsToCreate, this.icons);
    } else {
      // 只创建指定的图标
      iconNames.forEach(name => {
        if (this.icons[name]) {
          iconsToCreate[name] = this.icons[name];
        } else {
          console.warn(`Icon '${name}' not found in loaded icons`);
        }
      });
    }
    
    this.createIcons({ icons: iconsToCreate });
  }
  
  // 获取可用的图标列表
  static getAvailableIcons(): string[] {
    return Object.keys(this.icons);
  }
  
  // 检查图标是否可用
  static hasIcon(iconName: string): boolean {
    return iconName in this.icons;
  }
}

// 全局初始化函数，供HTML页面使用
declare global {
  interface Window {
    initializeIcons: (iconNames?: string[]) => Promise<void>;
  }
}

if (typeof window !== 'undefined') {
  window.initializeIcons = async function(iconNames?: string[]) {
    const success = await IconManager.initialize();
    if (success) {
      IconManager.create(iconNames);
    }
  };
}